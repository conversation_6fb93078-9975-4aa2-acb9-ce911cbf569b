/** @format */

const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet'); // ✅ added helmet
const rateLimit = require('express-rate-limit'); // ✅ global rate limiter
const cloudinary = require('cloudinary').v2;
const multer = require('multer');
const mongoose = require('mongoose');
const path = require('path');

dotenv.config();

// Existing routes
const { Auth } = require('./routes/Auth');
const { AdminAuth } = require('./routes/Admin/Auth');
const { Campaigns } = require('./routes/Admin/campaigns');
const { application } = require('./routes/Admin/Applications');
const { registered } = require('./routes/Admin/registered');
const { adminRouter } = require('./routes/Admin/rewardsAdmin');
const { user } = require('./routes/user/user');
const { CampaignsPublic } = require('./middlewares/public');
const campaignRoutes = require('./routes/user/campaignsubmission');
const adminBrandApproveRoutes = require('./routes/Admin/brands');
const adminBrandPlanRoutes = require('./routes/Admin/brandPlanManagement');
const adminPaymentRoutes = require('./routes/Admin/payments');

// Brand payment routes
const brandPaymentRoutes = require('./routes/brand/payments');

// New brand routes
const brandAuthRouter = require('./routes/brand/auth');
const brandPackageRouter = require('./routes/brand/package');
const brandDashboardRouter = require('./routes/brand/dashboard');
const brandTrackingRouter = require('./routes/brand/tracking');
const brandApplicationRoutes = require('./routes/brand/applications');
const brandCampaignRequestRoutes = require('./routes/brand/campaignRequest');
const adminCampaignApprovalRoutes = require('./routes/Admin/campaignRequest');
const brandSettingsRouter = require('./routes/brand/settings');
const brandCampaignApplyRoutes = require('./routes/brand/brandCampaignApplyRoutes');
const brandSubmissionRoutes = require("./routes/brand/brandSubmissionRoutes");
const { brandUsers } = require("./routes/brand/brandUsers");

// Webhook route
const webhooks = require('./routes/brand/webhooks');

// File upload setup
const upload = multer({ dest: 'uploads/' });

// ✅ CORS Origins for Railway deployment
const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:3001',
  'https://localhost:3000',
  'https://localhost:3001',
  'https://guideway-consulting.vercel.app',              // ✅ Production Vercel frontend
  'https://guideway-consulting-git-main-tahas-projects-a2b2c3d4.vercel.app', // ✅ Vercel preview URLs
  'https://devboost-guideway-consulting.onrender.com',   // ✅ Legacy URL (if still needed)
  'http://devboost-guideway-consulting.onrender.com'     // ✅ Legacy HTTP variant
];

const PORT = process.env.PORT || 5000; // ✅ Added fallback port

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

const app = express();

// ✅ FIXED: Configure Helmet to allow your frontend
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" },
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// ✅ Health check route (before rate limiting and CORS)
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    port: PORT,
    environment: process.env.NODE_ENV || 'development'
  });
});

// ✅ Global rate limiter (200 requests/min per IP) - AFTER health check
const globalLimiter = rateLimit({
  windowMs: 60 * 1000,
  max: 200,
  message: { status: 'failed', message: 'Too many requests. Try again in 1 minute.' },
  skip: (req) => req.path === '/health' // Skip rate limiting for health check
});
app.use(globalLimiter);

// ✅ FIXED: Enhanced CORS setup with better error handling
app.use(
  cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (mobile apps, curl, Postman, etc.)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.log('❌ CORS blocked origin:', origin);
        callback(new Error(`Not allowed by CORS. Origin: ${origin}`));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'Cache-Control',
      'X-Access-Token'
    ],
  })
);

// ✅ Handle preflight requests explicitly
app.options('*', cors());

// ✅ Stripe webhook must be mounted before JSON parsing
app.use('/webhooks', webhooks);

// ✅ JSON body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ✅ FIXED: Serve static files with proper MIME types
app.use(express.static(path.join(__dirname, 'dist'), {
  setHeaders: (res, filePath) => {
    if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css');
    } else if (filePath.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript');
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html');
    }
  }
}));

// ✅ Root test route
app.get('/', (req, res) => {
  res.json({ app: 'app is live', timestamp: new Date().toISOString() });
});



// ✅ Existing API routes
app.use('/api/auth', Auth);
app.use('/api/admin', AdminAuth);
app.use('/api/admin/campaigns', Campaigns);
app.use('/api/admin/applications', application);
app.use('/api/admin/users', registered);
app.use('/api/admin/referrel', adminRouter);
app.use('/api/user/campaigns', CampaignsPublic);
app.use('/api/user/campaigns', user);
app.use('/api/user', campaignRoutes);
app.use('/api/admin/brands', adminBrandApproveRoutes);
app.use('/api/admin/brands', adminBrandPlanRoutes);

// ✅ Admin payment routes
app.use('/api/admin/payments', adminPaymentRoutes);

// ✅ Brand-specific routes
app.use('/api/brand/auth', brandAuthRouter);
app.use('/api/brand/package', brandPackageRouter);
app.use('/api/brand/dashboard', brandDashboardRouter);
app.use('/api/brand/tracking', brandTrackingRouter);
app.use('/api/brand/payments', brandPaymentRoutes);
app.use('/api/brand/campaign-request', brandCampaignRequestRoutes);
app.use('/api/brand/applications', brandApplicationRoutes);
app.use('/api/brand', brandCampaignApplyRoutes);
app.use("/api", brandSubmissionRoutes);
app.use("/api/brand/users", brandUsers);

// ✅ Admin campaign approvals
app.use('/api/admin/campaign-approvals', adminCampaignApprovalRoutes);

// ✅ Brand settings
app.use('/api/brand/settings', brandSettingsRouter);

// ✅ File upload route with better error handling
app.post('/api/upload', upload.single('image'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ 
      status: 'failed',
      message: 'No file uploaded' 
    });
  }

  try {
    const result = await cloudinary.uploader.upload(req.file.path, {
      folder: 'uploads',
      resource_type: 'auto'
    });
    
    res.status(200).json({ 
      status: 'success',
      imageUrl: result.secure_url,
      publicId: result.public_id
    });
  } catch (error) {
    console.error('❌ Cloudinary upload error:', error);
    res.status(500).json({ 
      status: 'failed',
      message: 'Error uploading to Cloudinary',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// ✅ API 404 handler (must come before catch-all)
app.use('/api/*', (req, res) => {
  res.status(404).json({ 
    status: 'failed',
    message: `API route not found: ${req.method} ${req.path}` 
  });
});

// ✅ Catch-all: serve index.html for any non-API route (for React Router)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'), (err) => {
    if (err) {
      console.error('❌ Error serving index.html:', err);
      res.status(500).json({ message: 'Error serving application' });
    }
  });
});

// ✅ Global error handler
app.use((error, req, res, next) => {
  console.error('❌ Global error:', error);
  
  if (error.message && error.message.includes('CORS')) {
    return res.status(403).json({
      status: 'failed',
      message: 'CORS policy violation',
      origin: req.get('Origin')
    });
  }
  
  res.status(500).json({
    status: 'failed',
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// ✅ MongoDB connection & start server
console.log('🔗 Attempting to connect to MongoDB...');
mongoose
  .connect(process.env.MONGO_URL, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    serverSelectionTimeoutMS: 10000, // 10 seconds
    socketTimeoutMS: 45000, // 45 seconds
    maxPoolSize: 10,
    retryWrites: true,
  })
  .then(() => {
    console.log('✅ Connected to MongoDB');

    // Wait for connection to be ready before accessing database info
    mongoose.connection.once('open', () => {
      console.log('📊 Database:', mongoose.connection.db?.databaseName || 'Connected');
    });

    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log('🚀 Server running on port', PORT);
      console.log('🌐 Host: 0.0.0.0 (Railway requirement)');
      console.log('🌐 Allowed origins:', allowedOrigins);
      console.log('📁 Serving static files from:', path.join(__dirname, 'dist'));
      console.log('🏥 Health check available at: /health');
      console.log('✅ Server is ready to accept connections');
    });

    server.on('error', (error) => {
      console.error('❌ Server error:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
      }
      process.exit(1);
    });
  })
  .catch((err) => {
    console.error('❌ MongoDB connection failed:', err);
    console.error('❌ MONGO_URL:', process.env.MONGO_URL ? 'Set' : 'Not set');
    process.exit(1);
  });

// ✅ Graceful shutdown
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received, shutting down gracefully');
  mongoose.connection.close(() => {
    console.log('📦 MongoDB disconnected');
    process.exit(0);
  });
});