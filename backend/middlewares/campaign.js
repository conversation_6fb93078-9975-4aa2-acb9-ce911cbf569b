// Middleware for handling Campaign CRUD operations
const { Campaign, appliedCampaigns } = require("../database");
const { sendCampaignToAllUsers } = require("../functions/sendEmail"); // Uncomment to enable email broadcast

// Create a new campaign (Admin only)
async function addCampaign(req, res, next) {
  const { campaign } = req.body;
  try {
    console.log(campaign,"campaign");
    // Construct and save new Campaign document
    const buildCampaignPayload = (campaign) => ({
      // Basic Info
      campaignTitle: campaign.campaignTitle || "N/A",
      campaignIndustry: campaign.campaignIndustry || "N/A",
      campaignType: campaign.campaignType || "gifted",
      category: campaign.category || "N/A",
      brandName: campaign.brandName || "N/A",
    
      // Product Info
      productName: campaign.productName || "N/A",
      productDescription: campaign.productDescription || "N/A",
    
      // Media
      brandLogo: campaign.brandLogo || "N/A",
      productImages: Array.isArray(campaign.productImages) ? campaign.productImages : [],
    
      // Creator & Reach
      creatorCount: typeof campaign.creatorCount === "number" ? campaign.creatorCount : 10,
      recruiting: typeof campaign.recruiting === "number" ? campaign.recruiting : 0,
      influencersReceive: campaign.influencersReceive || "N/A",
    
      // Content & Tracking
      contentFormat: Array.isArray(campaign.contentFormat) ? campaign.contentFormat : [],
      requiredHashtags: Array.isArray(campaign.requiredHashtags) ? campaign.requiredHashtags : [],
      mentionHandle: campaign.mentionHandle || "N/A",
      toneGuide: campaign.toneGuide || "N/A",
      referenceContent: Array.isArray(campaign.referenceContent) ? campaign.referenceContent : [],
    
      // Deadlines
      deadline: campaign.deadline ? new Date(campaign.deadline) : new Date(),
      recruitmentEndDate: campaign.recruitmentEndDate ? new Date(campaign.recruitmentEndDate) : new Date(),
    
      // Requirements
      participationRequirements: campaign.participationRequirements || "N/A",
    
      // Status
      status: campaign.status || "Active",
    
      // Paid Campaign Fields
      pricingModel: campaign.pricingModel,
      fixedPrice: typeof campaign.fixedPrice === "number" ? campaign.fixedPrice : null,
      minBid: typeof campaign.minBid === "number" ? campaign.minBid : null,
      maxBid: typeof campaign.maxBid === "number" ? campaign.maxBid : null,
    
      // Requested Content
      requestedContent: {
        videos: campaign.requestedContent?.videos ?? 0,
        photos: campaign.requestedContent?.photos ?? 0,
        notes: campaign.requestedContent?.notes || "N/A",
      },
    
      // Creator Requirements
      creatorRequirements: {
        followerCount: campaign.creatorRequirements?.followerCount || "N/A",
        location: campaign.creatorRequirements?.location || "N/A",
        minAge: campaign.creatorRequirements?.minAge || "N/A",
        maxAge: campaign.creatorRequirements?.maxAge || "N/A",
        gender: campaign.creatorRequirements?.gender || "N/A",
      },
    
      // Usage Terms
      usageTerms: {
        usageRightsDuration: campaign.usageTerms?.usageRightsDuration || "N/A",
        lateSubmissionPenalty: campaign.usageTerms?.lateSubmissionPenalty || "N/A",
        paymentResponsibilityNotice:
          campaign.usageTerms?.paymentResponsibilityNotice ?? false,
        usageRights: Array.isArray(campaign.usageTerms?.usageRights)
          ? campaign.usageTerms.usageRights
          : [],
        usageRightsOther: campaign.usageTerms?.usageRightsOther || "N/A",
      },
    
      // Category-specific details
      beautyDetails: {
        productType: campaign.beautyDetails?.productType || "N/A",
        skinTypes: campaign.beautyDetails?.skinTypes || [],
        keyIngredients: campaign.beautyDetails?.keyIngredients || [],
        usageInstructions: campaign.beautyDetails?.usageInstructions || "N/A",
      },
      foodDetails: {
        preparationMethod: campaign.foodDetails?.preparationMethod || "N/A",
        dietaryTags: campaign.foodDetails?.dietaryTags || [],
        eatingScene: campaign.foodDetails?.eatingScene || "N/A",
      },
      beverageDetails: {
        servingType: campaign.beverageDetails?.servingType || "N/A",
        servingTemperature: campaign.beverageDetails?.servingTemperature || "N/A",
        caffeineContent: campaign.beverageDetails?.caffeineContent || "N/A",
        dietaryTags: campaign.beverageDetails?.dietaryTags || [],
      },
      wellnessDetails: {
        productType: campaign.wellnessDetails?.productType || "N/A",
        targetFunctions: campaign.wellnessDetails?.targetFunctions || [],
        formType: campaign.wellnessDetails?.formType || "N/A",
        usageTiming: campaign.wellnessDetails?.usageTiming || "N/A",
        flavor: campaign.wellnessDetails?.flavor || "N/A",
        dietaryTags: campaign.wellnessDetails?.dietaryTags || [],
      },
      personalCareDetails: {
        productType: campaign.personalCareDetails?.productType || "N/A",
        useAreas: campaign.personalCareDetails?.useAreas || [],
        keyIngredients: campaign.personalCareDetails?.keyIngredients || [],
        scent: campaign.personalCareDetails?.scent || "N/A",
        texture: campaign.personalCareDetails?.texture || "N/A",
      },
    
      // Reference ID
      referenceId: campaign.referenceId || null,
    });
    
    const payload = buildCampaignPayload(campaign);
    const newCampaign = new Campaign(payload);
    await newCampaign.save();

    // Respond success
    res.json({
      status: "success",
      message: "Campaign added successfully",
    });

    // Optionally send broadcast email to all users
    // setImmediate(() => sendCampaignToAllUsers(newCampaign));
  } catch (error) {
    console.error(error);
    // Handle validation errors
    if (error.name === 'ValidationError') {
      return res.json({ status: "failed", message: error.message });
    }
    // Handle generic errors
    return res.json({ status: "failed", message: "Something went wrong" });
  }
  next();
}

// Retrieve paginated list of campaigns with applicant counts (Admin)
async function getCampaigns(req, res, next) {
  const { page = 1 } = req.query;
  const limit = 100;
  const skip = (page - 1) * limit;
  try {
    const campaigns = await Campaign.find({}).skip(skip).limit(limit);

    if (!campaigns || campaigns.length === 0) {
      return res.json({ status: "failed", message: "No campaigns found" });
    }
    const totalCampaigns = await Campaign.countDocuments({});
    const totalPages = Math.ceil(totalCampaigns / limit);
    const isLastPage = page >= totalPages;
    // const result = campaigns.map(campaign => ({ /* … */ }));
    const result = await Promise.all(
      campaigns.map(async (campaignDoc) => {
        const applicantsCount = await appliedCampaigns.countDocuments({
          campaign: campaignDoc._id
        });   
        // ✅ Auto status check
        const now = new Date();
        const isRecruitmentClosed = campaignDoc.recruitmentEndDate && now > campaignDoc.recruitmentEndDate;
        const effectiveStatus = isRecruitmentClosed ? "Deactive" : campaignDoc.status;
        console.log(campaignDoc);

        return {
          campaignTitle: campaignDoc.campaignTitle,
          brandName: campaignDoc.brandName,
          productDescription: campaignDoc.productDescription,
          contentFormat: campaignDoc.contentFormat,
          requiredHashtags: campaignDoc.requiredHashtags,
          pricingModel: campaignDoc.pricingModel,
          price: campaignDoc.fixedPrice,
          recruiting: campaignDoc.recruiting,
          influencersReceive: campaignDoc.influencersReceive,
          campaignIndustry: campaignDoc.campaignIndustry,
          deadline: campaignDoc.deadline,
          participationRequirements: campaignDoc.participationRequirements,
          productImages: campaignDoc.productImages,
          applicantsCount,
          recruitmentEndDate: campaignDoc.recruitmentEndDate, // ✅ show in UI
          status: effectiveStatus, // ✅ return updated status
          id: campaignDoc._id,
          rights: campaignDoc.campaignType === 'paid' ? (campaignDoc.usageTerms?.usageRights || "") : "",
          campaignType: campaignDoc.campaignType,
        };
      })
    );    
    // 4) response
    res.json({
      status: "success",
      campaigns: result,
      totalPages,
      isLastPage,
    });
  } catch (error) {
    console.error("Error fetching campaigns:", error);
    res.json({ status: "failed", message: error.message });
  }
}


// Update an existing campaign (Admin only)
async function editCmpaigns(req, res, next) {
  const { id } = req.params;
  const { campaign } = req.body;

  try {
    if (!id || !campaign) {
      return res.status(400).json({
        status: "failed",
        message: "Missing campaign ID or data",
      });
    }

    // Destructure fields from incoming data
    const {
      campaignTitle,
      brandName,
      productDescription,
      contentFormat,
      requiredHashtags,
      campaignIndustry,
      influencersReceive,
      deadline,
      recruiting,
      participationRequirements,
      brandLogo,
      productImages,
      recruitmentEndDate, // ✅
      status,             // ✅
    } = campaign;

    // Build update payload dynamically and safely
    const updatePayload = {};

    if (campaignTitle) updatePayload.campaignTitle = campaignTitle;
    if (brandName) updatePayload.brandName = brandName;
    if (productDescription) updatePayload.productDescription = productDescription;
    if (Array.isArray(contentFormat)) updatePayload.contentFormat = contentFormat;
    if (Array.isArray(requiredHashtags)) updatePayload.requiredHashtags = requiredHashtags;
    if (campaignIndustry) updatePayload.campaignIndustry = campaignIndustry;
    if (influencersReceive) updatePayload.influencersReceive = influencersReceive;
    if (deadline) updatePayload.deadline = deadline;
    if (typeof recruiting === "number") updatePayload.recruiting = recruiting;
    if (participationRequirements) updatePayload.participationRequirements = participationRequirements;
    if (recruitmentEndDate) updatePayload.recruitmentEndDate = recruitmentEndDate;
    if (status) updatePayload.status = status;
    // ✅ Images (update only if passed)
    if (brandLogo) updatePayload.brandLogo = brandLogo;
    if (Array.isArray(productImages)) updatePayload.productImages = productImages;

    console.log("🛠 Final Payload for DB Update =>", updatePayload);

    const updated = await Campaign.findByIdAndUpdate(id, { $set: updatePayload }, { new: true });

    if (!updated) {
      return res.status(404).json({ status: "failed", message: "Campaign not found" });
    }

    return res.json({ status: "success", message: "Campaign updated successfully" });
  } catch (error) {
    console.error("❌ Error updating campaign:", error);
    return res.status(500).json({
      status: "failed",
      message: error.message || "Something went wrong",
    });
  }
}



// Delete a campaign by ID (Admin only)
async function deleteCampaigns(req, res, next) {
  const { id } = req.params;
  try {
    const deleted = await Campaign.findByIdAndDelete(id);
    if (!deleted) {
      return res.json({ status: "failed", message: "Campaign not found" });
    }
    res.json({ status: "success", message: "Campaign deleted successfully" });
  } catch (error) {
    console.error("Error deleting campaign:", error);
    res.json({ status: "failed", message: "Something went wrong" });
  }
}

// Get distinct brand names for filters (Admin only)
async function getBrands(req, res, next) {
  try {
    const brands = await Campaign.distinct('brandName');
    if (!brands || brands.length === 0) {
      return res.json({ status: "failed", message: "No brands found" });
    }
    res.json({ status: "success", brands });
  } catch (error) {
    console.error("Error fetching brands:", error);
    res.json({ status: "failed", message: "Something went wrong fetching brands" });
  }
}

module.exports = {
  addCampaign,
  getCampaigns,
  editCmpaigns,
  deleteCampaigns,
  getBrands,
};
