const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const { ApplyCampaign } = require("../../middlewares/ApplyCampaign");
const { Campaign, appliedCampaigns, User, campaignSubmission} = require("../../database"); // ✅ include User model
const bcrypt = require("bcrypt");
const userRouter = express.Router();
const rateLimit = require("express-rate-limit");
const {
  getReferralDashboard,
  getUserPoints,
  requestReward,
} = require("../../middlewares/referralController");
const { redeemReward } = require("../../middlewares/rewardRedeem");

// ⬅️ your referral controller 
const applyLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 100, // ✅ Allow up to 100 apply requests per IP
  message: {
    status: "failed",
    message: "Too many apply attempts from this IP. Please try again later.",
  },
});

/**
 * POST /user/apply
 * Apply to a campaign. Requires valid JWT token.
 * Body: { email, address, city, state, zip, campaignId, phone }
 */
userRouter.post("/apply", VerifyToken, applyLimiter, ApplyCampaign);


// View referral dashboard
userRouter.get("/referral", VerifyToken, getReferralDashboard);

// View current point balance + log
userRouter.get("/points", VerifyToken, getUserPoints);


// Request reward redemption
// userRouter.post("/redeem", VerifyToken, requestReward);

userRouter.post("/redeem", VerifyToken, redeemReward);



/**
 * GET /user/appliedCampaigns
 * Fetch all campaigns the authenticated user has applied to.
 * Requires valid JWT token.
 * Body: { email }
 */
userRouter.get("/appliedCampaigns", VerifyToken, async (req, res) => {
  const email = req.user.email;

  try {
    // 1. Get applied campaigns linked to this user
    const results = await appliedCampaigns
      .find({ email })
      .populate({
        path: "campaign",
        select: "campaignTitle campaignType recruitmentEndDate status",
        options: { strictPopulate: false },
      })
      .lean();

    // 2. Filter out entries with null campaign references
    const validApplications = results.filter(entry => entry.campaign);

    // 3. Map response with meaningful info
    const response = validApplications.map(item => {
      const { recruitmentEndDate, status } = item.campaign;
      
      // —————— DATE-ONLY COMPARISON ——————
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const deadlineDate = recruitmentEndDate
        ? new Date(recruitmentEndDate)
        : null;
      if (deadlineDate) deadlineDate.setHours(0, 0, 0, 0);

      const isExpired = deadlineDate
        ? deadlineDate < today
        : false;

      // अगर डेट एक्सपायर हो चुकी है, तो Deactive; वरना DB का status ही use करो
      const effectiveStatus = isExpired ? "Deactive" : status;

      console.log(item.campaign);
      return {
        id: item.campaign._id,
        title: item.campaign.campaignTitle,
        campaignStatus: effectiveStatus,   // अब Closed तभी आएगा जब सच में डेट पूरे हो जाए
        applicationStatus: item.status,
        rejectionReason: item.rejectionReason || null,
        showReasonToInfluencer: !!item.showReasonToInfluencer,
        // timezone adjust सिर्फ अगर ज़रूरत हो तो; नहीं तो सीधे ISO में छोड़ सकते हो
        appliedAt: item.createdAt.toISOString(),
        campaignType: item.campaign.campaignType,
      };
    });

    return res.json({ status: "success", campaigns: response });
  } catch (error) {
    console.error("❌ Error fetching applied campaigns:", error);
    return res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching your campaigns.",
    });
  }
});

/**
 * GET /user/access/paid-campaigns
 * Check if the user is eligible for paid campaigns (Matchably logic)
 */
userRouter.get("/access-paid-campaigns", VerifyToken, async (req, res) => {
  const email = req.user.email;
  try {
    // 1. Check for approved "Matchably Review Challenge"
    const matchablyChallengeCampaign = await Campaign.findOne({
      brandName: "Matchably",
      productName: "Matchably",
    }).lean();

    console.log(matchablyChallengeCampaign);
    let isMatchablyApproved = false;
    if (matchablyChallengeCampaign) {
      const application = await appliedCampaigns.findOne({
        email,
        campaign: matchablyChallengeCampaign._id,
        status: "Approved",
      });
      if (application) {
        isMatchablyApproved = true;
      }
    }

    // 2. Check for at least one completed gifted campaign
    const giftedCampaigns = await Campaign.find({
      $or: [
        { campaignType: "gifted" },
        { influencersReceive: /gifted/i },
      ],
    }).select("_id").lean();
    
    const giftedCampaignIds = giftedCampaigns.map(c => c._id);

    let hasGiftedCompleted = false;
    if (giftedCampaignIds.length > 0) {
      const giftedApplication = await appliedCampaigns.findOne({
        email,
        campaign: { $in: giftedCampaignIds },
        status: { $in: ["Submitted", "Approved"] },
      }).lean();

      if (giftedApplication) {
        hasGiftedCompleted = true;
      }
    }

    // Final access logic
    if (isMatchablyApproved && hasGiftedCompleted) {
      return res.json({ access: true });
    }

    let reason = "";
    if (!isMatchablyApproved && !hasGiftedCompleted) {
      reason = "To unlock Paid Campaigns, you need to complete the Matchably Review Challenge and at least one Gifted Campaign.";
    } else if (!isMatchablyApproved) {
      reason = "You still need to complete and get approved for the Matchably Review Challenge.";
    } else {
      reason = "You're almost there! Just complete one Gifted Campaign to unlock paid opportunities.";
    }
    
    return res.json({ access: false, reason });

  } catch (error) {
    console.error("❌ Error checking paid campaign access:", error);
    return res.status(500).json({ access: false, reason: "Server error" });
  }
});

/**
 * PUT /user/update-social
 * Update social media IDs for the logged-in user
 * Body: { instagramId, youtubeId, tiktokId }
 */
userRouter.put("/update-social", VerifyToken, async (req, res) => {
  const userId = req.user?._id;// ✅ comes from VerifyToken middleware
  const { instagramId, tiktokId } = req.body;

  try {
    await User.findByIdAndUpdate(userId, {
      instagramId,
      tiktokId,
    });

    return res.json({ status: "success", message: "Social media IDs updated successfully" });
  } catch (error) {
    console.error("Error updating social media IDs:", error);
    return res.status(500).json({
      status: "failed",
      message: "Could not update social media info",
    });
  }
});

userRouter.post("/change-password", VerifyToken, async (req, res) => {
  const { oldPassword, newPassword, confirmPassword } = req.body;
  const userId = req.user._id;

  if (!oldPassword || !newPassword || !confirmPassword) {
    return res.json({ status: "failed", message: "All fields are required" });
  }

  if (newPassword !== confirmPassword) {
    return res.json({ status: "failed", message: "Passwords do not match" });
  }

  try {
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ status: "failed", message: "User not found" });
    }

    if (!user.password) {
      return res.status(400).json({ status: "failed", message: "This account has no password set (Google login)." });
    }

    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      return res.json({ status: "failed", message: "Old password is incorrect" });
    }

    const hashed = await bcrypt.hash(newPassword, 10);
    user.password = hashed;
    await user.save();

    return res.json({ status: "success", message: "Password changed successfully" });
  } catch (err) {
    console.error("🔴 Password change error:", err.message, err.stack);
    return res.status(500).json({ status: "failed", message: "Error changing password" });
  }
});

/**
 * POST /user/campaigns/withdraw
 * Withdraw a campaign application (only if status is Pending)
 * Body: { campaignId }
 */
userRouter.post("/withdraw", VerifyToken, async (req, res) => {
  const email = req.user.email;
  const { campaignId } = req.body;

  if (!campaignId) {
    return res.status(400).json({ status: "failed", message: "Missing campaignId" });
  }

  try {
    // Delete the application
    const result = await appliedCampaigns.findOneAndDelete({ email, campaign: campaignId });

    if (!result) {
      return res.status(404).json({ status: "failed", message: "Application not found" });
    }

    return res.json({ status: "success", message: "Application withdrawn and deleted" });
  } catch (err) {
    console.error("Error withdrawing application:", err);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
});


module.exports = {
  user: userRouter,
};
