import React, { useState, useEffect } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import axios from 'axios';
import Cookies from 'js-cookie';
import config from '../config';

const AddPostUrl = () => {
  const { campaignId } = useParams();
  const { state } = useLocation();
  const campaignTitle = state?.campaignTitle ?? 'Untitled Campaign';
  const [rows, setRows] = useState([{ instagram: '', tiktok: '' }]);
  const [allowReuse, setAllowReuse] = useState(false);
  const [trackingInfo, setTrackingInfo] = useState({ courier: '', tracking_number: '' });
  const [exists, setExists] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  const getAuthHeader = () => {
    const token = Cookies.get('token') || localStorage.getItem('token');
    return { Authorization: token || '' };
  };

  useEffect(() => {
    const fetchSubmission = async () => {
      setLoading(true);
      try {
        const res = await axios.get(
          `${config.BACKEND_URL}/user/campaign-submission/${campaignId}`,
          { headers: getAuthHeader() }
        );
        if (res.data.status === 'success') {
          const {
            instagram_urls = [],
            tiktok_urls = [],
            allow_brand_reuse,
            tracking_info = {},
          } = res.data.data;

          const max = Math.max(instagram_urls.length, tiktok_urls.length, 1);
          setRows(
            Array.from({ length: max }, (_, i) => ({
              instagram: instagram_urls[i] || '',
              tiktok: tiktok_urls[i] || '',
            }))
          );

          setAllowReuse(!!allow_brand_reuse);
          setTrackingInfo({
            courier: tracking_info.courier || '',
            tracking_number: tracking_info.tracking_number || '',
          });

          setExists(true);
        } else {
          setRows([{ instagram: '', tiktok: '' }]);
          setAllowReuse(false);
          setExists(false);
        }
      } catch (err) {
        console.error(err);
        setError('Could not load your submission.');
      } finally {
        setLoading(false);
      }
    };
    fetchSubmission();
  }, [campaignId]);

  const openModal = () => {
    console.log("open");
    setIsModalOpen(true);}
  const closeModal = () => {
    setError('');
    setIsModalOpen(false);
  };

  const handleChange = (idx, field, value) => {
    setRows(rows.map((r, i) => (i === idx ? { ...r, [field]: value } : r)));
  };

  const addRow = () => {
    const totalUrls = rows.reduce(
      (count, row) => count + (row.instagram ? 1 : 0) + (row.tiktok ? 1 : 0),
      0
    );
    if (totalUrls >= 11) {
      setError('You can add a maximum of 11 URLs.');
      return;
    }
    setRows([...rows, { instagram: '', tiktok: '' }]);
  };

  const removeRow = (idx) => {
    setRows(rows.filter((_, i) => i !== idx));
  };

  const handleSave = async () => {
    setLoading(true);
    setError('');
    const instagram_urls = rows.map(r => r.instagram.trim()).filter(Boolean);
    const tiktok_urls = rows.map(r => r.tiktok.trim()).filter(Boolean);

    const totalUrls = instagram_urls.length + tiktok_urls.length;

    if (totalUrls === 0) {
      setError('At least one Instagram or TikTok URL is required.');
      setLoading(false);
      return;
    }

    if (totalUrls > 11) {
      setError('You can only submit up to 11 URLs.');
      setLoading(false);
      return;
    }

    const payload = {
      campaign_id: campaignId,
      instagram_urls,
      tiktok_urls,
      allow_brand_reuse: allowReuse,
    };

    try {
      await axios.post(
        `${config.BACKEND_URL}/user/campaign-submission`,
        payload,
        { headers: getAuthHeader() }
      );
      setExists(true);
      closeModal();
    } catch (err) {
      console.error(err);
      setError('Save failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Delete your submission?')) return;
    setLoading(true);
    setError('');
    try {
      await axios.delete(
        `${config.BACKEND_URL}/user/campaign-submission/${campaignId}`,
        { headers: getAuthHeader() }
      );
      setRows([{ instagram: '', tiktok: '' }]);
      setAllowReuse(false);
      setTrackingInfo({ courier: '', tracking_number: '' });
      setExists(false);
    } catch (err) {
      console.error(err);
      setError('Delete failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 flex flex-col items-center">
      <div className="w-full lg:w-3/4 bg-gray-900 rounded-lg shadow-md p-6 space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-semibold text-white">
            Campaign Name - {campaignTitle}
          </h2>
          <div className="space-x-2">
            {exists ? (
              <button
                onClick={handleDelete}
                disabled={loading}
                className="bg-red-600 hover:bg-red-500 text-white px-3 py-1 rounded"
              >
                {loading ? 'Deleting…' : 'Delete Submission'}
              </button>
            ) : (
              <button
                onClick={openModal}
                disabled={loading}
                className="bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-full font-bold"
              >
                + Add URLs
              </button>
            )}
          </div>
        </div>

        {error && <p className="text-red-400">{error}</p>}

        {loading && !isModalOpen ? (
          <p className="text-gray-400 text-center">Loading…</p>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full bg-gray-800 rounded-lg overflow-hidden">
                <thead>
                  <tr className="bg-gray-700">
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-200">Instagram</th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-200">TikTok</th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-200">Allow Reuse</th>
                  </tr>
                </thead>
                <tbody>
                  {rows.map((row, idx) => (
                    <tr key={idx} className="border-t border-gray-700 hover:bg-gray-700">
                      <td className="px-6 py-4 text-gray-100 break-all">
                        {row.instagram || <span className="text-gray-500 italic">–</span>}
                      </td>
                      <td className="px-6 py-4 text-gray-100 break-all">
                        {row.tiktok || <span className="text-gray-500 italic">–</span>}
                      </td>
                      {idx === 0 && (
                        <td className="px-6 py-4 text-gray-100">
                          {allowReuse ? 'Yes' : 'No'}
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {trackingInfo?.tracking_number && (
              <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 mt-4">
                <h4 className="text-lg font-semibold text-white mb-2">📦 Shipment Tracking</h4>
                <div className="text-gray-300 space-y-1">
                  <p>
                    <strong className="text-white">Courier:</strong> {trackingInfo.courier || 'N/A'}
                  </p>
                  <p>
                    <strong className="text-white">Tracking Number:</strong> {trackingInfo.tracking_number}
                  </p>
                  <p>
                    <a
                      href={`https://www.17track.net/en/track?nums=${trackingInfo.tracking_number}`}
                      target="_blank"
                      rel="noreferrer"
                      className="text-blue-400 underline hover:text-blue-300"
                    >
                      View Tracking ↗
                    </a>
                  </p>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
          <div className="w-full max-w-2xl space-y-4 rounded-lg bg-gray-900 p-6 shadow-xl">
            <h3 className="text-xl font-semibold text-white">
              {exists ? 'Edit Submission' : 'Add New Submission'}
            </h3>

            {error && <p className="rounded bg-red-900 p-3 text-red-300">{error}</p>}

            <div className="max-h-[60vh] space-y-3 overflow-y-auto p-1">
              {rows.map((row, idx) => (
                <div key={idx} className="flex items-start space-x-3">
                  <div className="flex-grow space-y-2">
                    <input
                      type="url"
                      placeholder="https://instagram.com/p/..."
                      value={row.instagram}
                      onChange={(e) => handleChange(idx, 'instagram', e.target.value)}
                      className="w-full rounded-lg border border-gray-700 bg-gray-800 px-3 py-2 text-white focus:border-blue-500 focus:ring-blue-500"
                    />
                    <input
                      type="url"
                      placeholder="https://www.tiktok.com/@user/video/..."
                      value={row.tiktok}
                      onChange={(e) => handleChange(idx, 'tiktok', e.target.value)}
                      className="w-full rounded-lg border border-gray-700 bg-gray-800 px-3 py-2 text-white focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  {rows.length > 1 && (
                    <button
                      onClick={() => removeRow(idx)}
                      className="mt-2 rounded-full p-2 text-gray-500 hover:bg-gray-700 hover:text-red-400"
                      aria-label="Remove URL row"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  )}
                </div>
              ))}
            </div>

            <div className="flex justify-between">
              <button
                onClick={addRow}
                className="text-sm text-blue-400 hover:text-blue-300"
              >
                + Add Another URL
              </button>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="allowReuse"
                  checked={allowReuse}
                  onChange={(e) => setAllowReuse(e.target.checked)}
                  className="h-4 w-4 rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-600"
                />
                <label htmlFor="allowReuse" className="text-gray-300">
                  Allow brand to reuse my content
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-4 border-t border-gray-700 pt-4">
              <button
                onClick={closeModal}
                className="rounded-lg px-4 py-2 text-gray-400 hover:bg-gray-800 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={loading}
                className="rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-500 disabled:cursor-not-allowed disabled:bg-gray-600"
              >
                {loading ? 'Saving...' : 'Save Submission'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddPostUrl;
