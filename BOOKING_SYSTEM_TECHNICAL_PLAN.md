# Booking System Integration & Ticket Verification Modernization
## Comprehensive Technical Implementation Plan

### Executive Summary
This document provides a detailed technical roadmap for unifying Great Lakes Schooner Company's fragmented booking ecosystem and modernizing ticket verification processes through centralized dashboard integration and advanced QR code scanning capabilities.

## 1. System Architecture Overview

### 1.1 Current State Analysis
**Existing Booking Channels:**
- Groupon (Third-party marketplace)
- Viator (Booking platform)
- Custom website forms (OrderSecureTickets)
- Phone bookings (Manual entry)
- Walk-in bookings

**Current Pain Points:**
- Fragmented booking data across multiple systems
- Incomplete QR code information (missing fare type, passenger count, cruise time)
- No real-time inventory synchronization
- Manual reconciliation processes
- Limited operational visibility

### 1.2 Target Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Centralized Booking Hub                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Viator    │  │   Groupon   │  │   Website   │         │
│  │ Integration │  │ Integration │  │   Widgets   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│              Real-time Inventory Management                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Mobile    │  │  Dashboard  │  │   Reports   │         │
│  │   Scanner   │  │   Portal    │  │ & Analytics │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 2. Platform Selection & Technical Specifications

### 2.1 Recommended Platform: FareHarbor
**Selection Rationale:**
- Zero monthly fees (revenue-based pricing)
- Native Viator integration (owned by TripAdvisor/Viator)
- Robust API ecosystem
- Proven maritime industry experience
- Comprehensive mobile scanning solution

**Technical Specifications:**
- **API Version:** REST API v1.0
- **Authentication:** OAuth 2.0 + API Keys
- **Rate Limits:** 1000 requests/minute
- **Data Format:** JSON
- **Webhook Support:** Real-time booking notifications
- **Mobile SDK:** iOS/Android native libraries

### 2.2 Alternative Platforms Comparison

| Feature | FareHarbor | Peek Pro | Rezdy |
|---------|------------|----------|-------|
| Monthly Cost | $0 | $99+ | $49-119 |
| Transaction Fee | 6% | 2-6% | 3-5% |
| Viator Integration | Native | API | API |
| Groupon Support | Manual | API | Limited |
| Mobile Scanner | Yes | Yes | Yes |
| Custom Fields | Advanced | Basic | Moderate |
| API Quality | Excellent | Good | Good |

## 3. Detailed Integration Architecture

### 3.1 Booking Flow Integration
```mermaid
graph TD
    A[Customer] --> B{Booking Channel}
    B -->|Website| C[FareHarbor Widget]
    B -->|Viator| D[Viator API]
    B -->|Groupon| E[Manual Import/CSV]
    B -->|Phone| F[Staff Dashboard]
    
    C --> G[FareHarbor Core]
    D --> G
    E --> G
    F --> G
    
    G --> H[Inventory Management]
    G --> I[QR Code Generation]
    G --> J[Customer Notifications]
    
    H --> K[Real-time Sync]
    I --> L[Enhanced QR Data]
    J --> M[Email/SMS]
```

### 3.2 Data Schema Design
```sql
-- Core Booking Entity
CREATE TABLE bookings (
    id UUID PRIMARY KEY,
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    source_channel VARCHAR(20) NOT NULL, -- 'viator', 'groupon', 'website', 'phone'
    cruise_id UUID NOT NULL,
    booking_date TIMESTAMP NOT NULL,
    cruise_date DATE NOT NULL,
    cruise_time TIME NOT NULL,
    total_passengers INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_status VARCHAR(20) DEFAULT 'confirmed',
    qr_code_data TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Passenger Details
CREATE TABLE passengers (
    id UUID PRIMARY KEY,
    booking_id UUID REFERENCES bookings(id),
    passenger_name VARCHAR(100) NOT NULL,
    fare_type VARCHAR(50) NOT NULL, -- 'adult', 'child', 'senior', 'group'
    age INTEGER,
    special_requirements TEXT,
    checked_in BOOLEAN DEFAULT FALSE,
    check_in_time TIMESTAMP
);

-- Cruise Inventory
CREATE TABLE cruises (
    id UUID PRIMARY KEY,
    cruise_name VARCHAR(100) NOT NULL,
    vessel_name VARCHAR(50) NOT NULL,
    departure_time TIME NOT NULL,
    duration_minutes INTEGER NOT NULL,
    max_capacity INTEGER NOT NULL,
    available_slots INTEGER NOT NULL,
    base_price DECIMAL(8,2) NOT NULL,
    active BOOLEAN DEFAULT TRUE
);
```

## 4. QR Code Enhancement Strategy

### 4.1 Enhanced QR Code Data Structure
```json
{
  "version": "2.0",
  "booking_id": "GLSCo-2024-001234",
  "booking_reference": "VIATOR-ABC123",
  "cruise_date": "2024-07-15",
  "cruise_time": "14:00",
  "vessel": "Kajama",
  "passengers": [
    {
      "name": "John Doe",
      "fare_type": "adult",
      "age": 35
    },
    {
      "name": "Jane Doe", 
      "fare_type": "adult",
      "age": 32
    }
  ],
  "total_count": 2,
  "booking_source": "viator",
  "special_notes": "Wheelchair accessible",
  "verification_hash": "sha256_hash_for_security"
}
```

### 4.2 QR Code Generation Process
```javascript
// QR Code Generation Service
class QRCodeService {
  static async generateBookingQR(bookingData) {
    const qrPayload = {
      version: "2.0",
      booking_id: bookingData.id,
      booking_reference: bookingData.reference,
      cruise_date: bookingData.cruiseDate,
      cruise_time: bookingData.cruiseTime,
      vessel: bookingData.vessel,
      passengers: bookingData.passengers.map(p => ({
        name: p.name,
        fare_type: p.fareType,
        age: p.age
      })),
      total_count: bookingData.passengers.length,
      booking_source: bookingData.source,
      special_notes: bookingData.specialRequirements,
      verification_hash: this.generateHash(bookingData)
    };
    
    const qrString = JSON.stringify(qrPayload);
    const qrCode = await QRCode.toDataURL(qrString, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      width: 256
    });
    
    return qrCode;
  }
  
  static generateHash(data) {
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data) + process.env.QR_SECRET)
      .digest('hex')
      .substring(0, 16);
  }
}
```

## 5. Mobile Scanner Application

### 5.1 Technology Stack
- **Framework:** React Native 0.72+
- **QR Scanner:** react-native-qrcode-scanner
- **State Management:** Redux Toolkit
- **API Client:** Axios with interceptors
- **Offline Support:** Redux Persist + AsyncStorage
- **Authentication:** JWT tokens
- **Push Notifications:** React Native Firebase

### 5.2 Scanner App Architecture
```
src/
├── components/
│   ├── QRScanner/
│   │   ├── ScannerView.tsx
│   │   ├── ScanResult.tsx
│   │   └── ScanHistory.tsx
│   ├── BookingDetails/
│   │   ├── PassengerList.tsx
│   │   ├── CruiseInfo.tsx
│   │   └── CheckInButton.tsx
│   └── Common/
│       ├── LoadingSpinner.tsx
│       └── ErrorBoundary.tsx
├── services/
│   ├── api/
│   │   ├── bookingService.ts
│   │   ├── authService.ts
│   │   └── syncService.ts
│   ├── scanner/
│   │   ├── qrDecoder.ts
│   │   └── dataValidator.ts
│   └── storage/
│       ├── offlineStorage.ts
│       └── cacheManager.ts
├── store/
│   ├── slices/
│   │   ├── authSlice.ts
│   │   ├── bookingSlice.ts
│   │   └── scannerSlice.ts
│   └── store.ts
└── utils/
    ├── constants.ts
    ├── helpers.ts
    └── validators.ts

### 5.3 Core Scanner Component Implementation
```typescript
// ScannerView.tsx
import React, { useState, useEffect } from 'react';
import { View, Text, Alert, StyleSheet } from 'react-native';
import QRCodeScanner from 'react-native-qrcode-scanner';
import { useDispatch, useSelector } from 'react-redux';
import { scanBooking, validateQRData } from '../store/slices/scannerSlice';

interface ScannerViewProps {
  onScanSuccess: (bookingData: any) => void;
}

const ScannerView: React.FC<ScannerViewProps> = ({ onScanSuccess }) => {
  const dispatch = useDispatch();
  const { isScanning, lastScanResult } = useSelector(state => state.scanner);

  const handleQRScan = async (e: any) => {
    try {
      const qrData = JSON.parse(e.data);

      // Validate QR code structure
      if (!validateQRData(qrData)) {
        Alert.alert('Invalid QR Code', 'This QR code is not valid for check-in.');
        return;
      }

      // Dispatch scan action
      const result = await dispatch(scanBooking(qrData));

      if (result.type === 'scanner/scanBooking/fulfilled') {
        onScanSuccess(result.payload);
      } else {
        Alert.alert('Scan Failed', result.payload.message);
      }
    } catch (error) {
      Alert.alert('Scan Error', 'Unable to read QR code data.');
    }
  };

  return (
    <View style={styles.container}>
      <QRCodeScanner
        onRead={handleQRScan}
        flashMode={QRCodeScanner.Constants.FlashMode.auto}
        topContent={
          <Text style={styles.centerText}>
            Scan passenger QR code to check in
          </Text>
        }
        bottomContent={
          <View style={styles.buttonContainer}>
            <Text style={styles.instructions}>
              Position QR code within the frame
            </Text>
          </View>
        }
        cameraStyle={styles.camera}
        markerStyle={styles.marker}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  centerText: {
    flex: 1,
    fontSize: 18,
    padding: 32,
    color: '#777',
    textAlign: 'center',
  },
  buttonContainer: {
    padding: 16,
  },
  instructions: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
  },
  camera: {
    height: '70%',
  },
  marker: {
    borderColor: '#00ff00',
    borderWidth: 2,
  },
});

export default ScannerView;
```

## 6. Backend API Development

### 6.1 Technology Stack
- **Runtime:** Node.js 18+ with TypeScript
- **Framework:** Express.js with Helmet security
- **Database:** PostgreSQL 14+ with Prisma ORM
- **Authentication:** JWT with refresh tokens
- **Caching:** Redis for session management
- **File Storage:** AWS S3 for QR codes and documents
- **Monitoring:** Winston logging + Sentry error tracking
- **Testing:** Jest + Supertest

### 6.2 API Endpoints Specification

```typescript
// Booking Management Endpoints
interface BookingAPI {
  // Core booking operations
  'GET /api/v1/bookings': GetBookingsResponse;
  'GET /api/v1/bookings/:id': GetBookingResponse;
  'POST /api/v1/bookings': CreateBookingRequest;
  'PUT /api/v1/bookings/:id': UpdateBookingRequest;
  'DELETE /api/v1/bookings/:id': DeleteBookingResponse;

  // Check-in operations
  'POST /api/v1/bookings/:id/checkin': CheckInRequest;
  'GET /api/v1/bookings/:id/status': BookingStatusResponse;

  // QR code operations
  'GET /api/v1/bookings/:id/qr': QRCodeResponse;
  'POST /api/v1/qr/validate': ValidateQRRequest;

  // Integration endpoints
  'POST /api/v1/integrations/viator/webhook': ViatorWebhookRequest;
  'POST /api/v1/integrations/groupon/import': GrouponImportRequest;

  // Reporting endpoints
  'GET /api/v1/reports/daily': DailyReportResponse;
  'GET /api/v1/reports/cruise/:cruiseId': CruiseReportResponse;
}

// Request/Response Types
interface CreateBookingRequest {
  cruiseId: string;
  passengers: PassengerData[];
  contactInfo: ContactInfo;
  source: 'website' | 'viator' | 'groupon' | 'phone';
  specialRequirements?: string;
}

interface CheckInRequest {
  qrData: string;
  checkInLocation: string;
  staffMemberId: string;
}

interface PassengerData {
  name: string;
  fareType: 'adult' | 'child' | 'senior' | 'group';
  age?: number;
  specialNeeds?: string;
}
```

### 6.3 Core Service Implementation
```typescript
// BookingService.ts
import { PrismaClient } from '@prisma/client';
import { QRCodeService } from './QRCodeService';
import { NotificationService } from './NotificationService';

export class BookingService {
  private prisma: PrismaClient;
  private qrService: QRCodeService;
  private notificationService: NotificationService;

  constructor() {
    this.prisma = new PrismaClient();
    this.qrService = new QRCodeService();
    this.notificationService = new NotificationService();
  }

  async createBooking(bookingData: CreateBookingRequest): Promise<Booking> {
    const booking = await this.prisma.booking.create({
      data: {
        bookingReference: this.generateBookingReference(),
        sourceChannel: bookingData.source,
        cruiseId: bookingData.cruiseId,
        totalPassengers: bookingData.passengers.length,
        bookingStatus: 'confirmed',
        passengers: {
          create: bookingData.passengers.map(p => ({
            passengerName: p.name,
            fareType: p.fareType,
            age: p.age,
            specialRequirements: p.specialNeeds,
          })),
        },
      },
      include: {
        passengers: true,
        cruise: true,
      },
    });

    // Generate QR code
    const qrCode = await this.qrService.generateBookingQR(booking);
    await this.prisma.booking.update({
      where: { id: booking.id },
      data: { qrCodeData: qrCode },
    });

    // Send confirmation
    await this.notificationService.sendBookingConfirmation(booking);

    return booking;
  }

  async checkInPassengers(qrData: string, checkInInfo: CheckInRequest): Promise<CheckInResult> {
    // Validate QR code
    const bookingData = await this.qrService.validateQRCode(qrData);

    if (!bookingData.isValid) {
      throw new Error('Invalid QR code');
    }

    // Update check-in status
    const booking = await this.prisma.booking.update({
      where: { id: bookingData.bookingId },
      data: {
        passengers: {
          updateMany: {
            where: { bookingId: bookingData.bookingId },
            data: {
              checkedIn: true,
              checkInTime: new Date(),
            },
          },
        },
      },
      include: {
        passengers: true,
        cruise: true,
      },
    });

    return {
      success: true,
      booking,
      checkedInCount: booking.passengers.length,
    };
  }

  private generateBookingReference(): string {
    const prefix = 'GLSCo';
    const year = new Date().getFullYear();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}-${year}-${random}`;
  }
}
```

## 7. Integration Implementation

### 7.1 Viator Integration
```typescript
// ViatorIntegration.ts
export class ViatorIntegration {
  private apiKey: string;
  private baseUrl: string = 'https://api.viator.com/partner';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async syncBookings(): Promise<void> {
    const bookings = await this.fetchRecentBookings();

    for (const viatorBooking of bookings) {
      await this.processViatorBooking(viatorBooking);
    }
  }

  private async fetchRecentBookings(): Promise<ViatorBooking[]> {
    const response = await fetch(`${this.baseUrl}/bookings`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    return response.json();
  }

  private async processViatorBooking(viatorBooking: ViatorBooking): Promise<void> {
    const bookingService = new BookingService();

    const localBooking = await bookingService.createBooking({
      cruiseId: this.mapViatorProductToCruise(viatorBooking.productId),
      passengers: viatorBooking.travelers.map(t => ({
        name: `${t.firstName} ${t.lastName}`,
        fareType: this.mapViatorFareType(t.ageBand),
        age: t.age,
      })),
      contactInfo: {
        email: viatorBooking.bookerEmail,
        phone: viatorBooking.bookerPhone,
      },
      source: 'viator',
    });

    // Store Viator reference
    await this.storeViatorReference(localBooking.id, viatorBooking.bookingRef);
  }
}
```

### 7.2 Website Widget Integration
```html
<!-- FareHarbor Widget Integration -->
<div id="fareharbor-widget-container"></div>

<script>
(function() {
  var script = document.createElement('script');
  script.src = 'https://fareharbor.com/embeds/api/v1/?autolightframe=yes';
  script.async = true;

  script.onload = function() {
    FareHarbor.init({
      shortname: 'greatlakesschooner',
      fallback: 'simple',
      container: '#fareharbor-widget-container',
      flow: 'overlay',

      // Custom styling
      primaryColor: '#1e40af',
      backgroundColor: '#ffffff',
      textColor: '#374151',

      // Event handlers
      onBookingComplete: function(booking) {
        // Track conversion
        gtag('event', 'purchase', {
          transaction_id: booking.display_id,
          value: booking.total,
          currency: 'CAD',
          items: [{
            item_id: booking.item.name,
            item_name: booking.item.display_name,
            quantity: booking.customers.length,
            price: booking.total
          }]
        });

        // Redirect to confirmation
        window.location.href = `/booking-confirmation?ref=${booking.display_id}`;
      },

      onBookingError: function(error) {
        console.error('Booking error:', error);
        // Show user-friendly error message
      }
    });
  };

  document.head.appendChild(script);
})();
</script>
```

## 8. Database Design & Migration Strategy

### 8.1 Complete Database Schema
```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Vessels table
CREATE TABLE vessels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    capacity INTEGER NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Cruise schedules
CREATE TABLE cruise_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vessel_id UUID REFERENCES vessels(id),
    cruise_name VARCHAR(100) NOT NULL,
    departure_time TIME NOT NULL,
    duration_minutes INTEGER NOT NULL,
    days_of_week INTEGER[] NOT NULL, -- [1,2,3,4,5,6,7] for Mon-Sun
    season_start DATE NOT NULL,
    season_end DATE NOT NULL,
    base_price DECIMAL(8,2) NOT NULL,
    active BOOLEAN DEFAULT TRUE
);

-- Daily cruise instances
CREATE TABLE cruises (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    schedule_id UUID REFERENCES cruise_schedules(id),
    cruise_date DATE NOT NULL,
    departure_time TIME NOT NULL,
    max_capacity INTEGER NOT NULL,
    available_slots INTEGER NOT NULL,
    weather_status VARCHAR(20) DEFAULT 'scheduled', -- scheduled, cancelled, delayed
    captain_notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(schedule_id, cruise_date)
);

-- Fare types and pricing
CREATE TABLE fare_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL, -- adult, child, senior, group
    description TEXT,
    age_min INTEGER,
    age_max INTEGER,
    price_modifier DECIMAL(5,2) DEFAULT 1.00, -- multiplier for base price
    active BOOLEAN DEFAULT TRUE
);

-- Main bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    source_channel VARCHAR(20) NOT NULL,
    external_reference VARCHAR(100), -- Viator/Groupon booking ID
    cruise_id UUID REFERENCES cruises(id),
    booking_date TIMESTAMP NOT NULL DEFAULT NOW(),
    total_passengers INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CAD',
    booking_status VARCHAR(20) DEFAULT 'confirmed',
    payment_status VARCHAR(20) DEFAULT 'pending',
    qr_code_data TEXT,
    special_requirements TEXT,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    contact_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Passenger details
CREATE TABLE passengers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
    passenger_name VARCHAR(100) NOT NULL,
    fare_type_id UUID REFERENCES fare_types(id),
    age INTEGER,
    special_requirements TEXT,
    checked_in BOOLEAN DEFAULT FALSE,
    check_in_time TIMESTAMP,
    check_in_staff_id UUID,
    seat_assignment VARCHAR(10),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Staff/users table
CREATE TABLE staff (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role VARCHAR(20) NOT NULL, -- admin, captain, crew, ticket_agent
    active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Check-in logs
CREATE TABLE check_in_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID REFERENCES bookings(id),
    passenger_id UUID REFERENCES passengers(id),
    staff_id UUID REFERENCES staff(id),
    check_in_time TIMESTAMP DEFAULT NOW(),
    check_in_method VARCHAR(20), -- qr_scan, manual, kiosk
    device_info JSONB,
    location VARCHAR(100)
);

-- Integration sync logs
CREATE TABLE sync_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    integration_name VARCHAR(50) NOT NULL,
    sync_type VARCHAR(20) NOT NULL, -- full, incremental, webhook
    status VARCHAR(20) NOT NULL, -- success, failed, partial
    records_processed INTEGER DEFAULT 0,
    errors_count INTEGER DEFAULT 0,
    error_details JSONB,
    started_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_bookings_cruise_date ON bookings(cruise_id);
CREATE INDEX idx_bookings_reference ON bookings(booking_reference);
CREATE INDEX idx_bookings_source ON bookings(source_channel);
CREATE INDEX idx_passengers_booking ON passengers(booking_id);
CREATE INDEX idx_passengers_checkin ON passengers(checked_in, check_in_time);
CREATE INDEX idx_cruises_date ON cruises(cruise_date);
CREATE INDEX idx_checkin_logs_time ON check_in_logs(check_in_time);
```

### 8.2 Migration Strategy
```typescript
// Migration scripts using Prisma
export const migrations = [
  {
    version: '001_initial_schema',
    up: async (prisma: PrismaClient) => {
      // Create initial tables
      await prisma.$executeRaw`
        -- Execute initial schema creation
        ${initialSchemaSQL}
      `;
    },
    down: async (prisma: PrismaClient) => {
      // Rollback script
      await prisma.$executeRaw`DROP SCHEMA public CASCADE; CREATE SCHEMA public;`;
    }
  },
  {
    version: '002_seed_data',
    up: async (prisma: PrismaClient) => {
      // Seed vessels
      await prisma.vessel.createMany({
        data: [
          { name: 'Kajama', capacity: 150, description: 'Historic tall ship' },
          { name: 'Empire Sandy', capacity: 100, description: 'Classic schooner' }
        ]
      });

      // Seed fare types
      await prisma.fareType.createMany({
        data: [
          { name: 'adult', description: 'Adult fare', ageMin: 13, ageMax: 64, priceModifier: 1.00 },
          { name: 'child', description: 'Child fare', ageMin: 3, ageMax: 12, priceModifier: 0.75 },
          { name: 'senior', description: 'Senior fare', ageMin: 65, ageMax: 120, priceModifier: 0.90 },
          { name: 'infant', description: 'Infant (free)', ageMin: 0, ageMax: 2, priceModifier: 0.00 }
        ]
      });
    }
  }
];
```
```
